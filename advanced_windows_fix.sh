#!/bin/bash

# 高级Windows CB4错误修复脚本

echo "=== 高级Windows CB4错误修复工具 ==="

# 确保分区已挂载
if ! mountpoint -q /mnt/windows; then
    sudo mkdir -p /mnt/windows
    sudo mount /dev/nvme0n1p2 /mnt/windows
fi

if ! mountpoint -q /mnt/efi; then
    sudo mkdir -p /mnt/efi
    sudo mount /dev/nvme0n1p1 /mnt/efi
fi

echo "=== 步骤1: 修复BCD启动配置数据库 ==="

# 备份原始BCD
sudo cp /mnt/efi/EFI/Microsoft/Boot/BCD /mnt/efi/EFI/Microsoft/Boot/BCD.original

# 尝试重建BCD
echo "重建BCD配置..."

# 创建新的BCD存储
sudo rm -f /mnt/efi/EFI/Microsoft/Boot/BCD.new

# 使用chntpw查看BCD结构（如果可能）
echo "检查BCD结构..."

echo "=== 步骤2: 创建Windows PE启动选项 ==="

# 创建一个安全模式启动项
echo "创建安全模式启动配置..."

# 检查Windows启动管理器
if [ -f "/mnt/efi/EFI/Microsoft/Boot/bootmgfw.efi" ]; then
    echo "✓ Windows启动管理器存在"
else
    echo "✗ Windows启动管理器缺失"
fi

echo "=== 步骤3: 修复驱动程序签名问题 ==="

# 检查关键驱动文件
echo "检查关键驱动程序..."
drivers_to_check=(
    "ntoskrnl.exe"
    "hal.dll" 
    "drivers/disk.sys"
    "drivers/Classpnp.sys"
    "drivers/partmgr.sys"
    "drivers/volmgr.sys"
    "drivers/volmgrx.sys"
    "drivers/mountmgr.sys"
    "drivers/fltmgr.sys"
)

for driver in "${drivers_to_check[@]}"; do
    if [ -f "/mnt/windows/Windows/System32/$driver" ]; then
        echo "✓ $driver 存在"
    else
        echo "✗ $driver 缺失"
    fi
done

echo "=== 步骤4: 重新配置启动项 ==="

# 删除所有Windows启动项
echo "清理现有启动项..."
for boot_num in $(efibootmgr | grep -i windows | cut -d'*' -f1 | sed 's/Boot//'); do
    sudo efibootmgr -b $boot_num -B 2>/dev/null
done

# 重新创建Windows启动项
echo "重新创建Windows启动项..."
sudo efibootmgr -c -d /dev/nvme0n1 -p 1 -L "Windows 10" -l "\\EFI\\Microsoft\\Boot\\bootmgfw.efi"

# 设置启动顺序
echo "设置启动顺序..."
windows_boot=$(efibootmgr | grep -i "Windows 10" | cut -d'*' -f1 | sed 's/Boot//')
ubuntu_boot=$(efibootmgr | grep -i ubuntu | cut -d'*' -f1 | sed 's/Boot//')

if [ ! -z "$windows_boot" ] && [ ! -z "$ubuntu_boot" ]; then
    sudo efibootmgr -o $ubuntu_boot,$windows_boot
fi

echo "=== 步骤5: 创建恢复启动脚本 ==="

# 创建一个Windows恢复脚本
cat > /tmp/windows_recovery.txt << 'EOF'
Windows 10 CB4错误恢复指南：

如果系统仍然无法启动，请尝试以下步骤：

1. 进入BIOS/UEFI设置：
   - 重启时按F2/F12/Del键进入BIOS
   - 禁用安全启动 (Secure Boot)
   - 启用传统启动支持 (Legacy Boot Support)
   - 保存并退出

2. 使用Windows安装盘修复：
   - 制作Windows 10安装U盘
   - 从U盘启动
   - 选择"修复计算机" -> "疑难解答" -> "高级选项"
   - 运行"启动修复"和"系统文件检查器"

3. 安全模式启动：
   - 在GRUB菜单中选择Windows
   - 如果出现蓝屏，强制重启3次
   - Windows会自动进入恢复模式
   - 选择"疑难解答" -> "高级选项" -> "启动设置"
   - 重启后按F4进入安全模式

4. 命令行修复（在Windows PE或安全模式下）：
   sfc /scannow
   dism /online /cleanup-image /restorehealth
   bootrec /fixmbr
   bootrec /fixboot
   bootrec /rebuildbcd
EOF

echo "恢复指南已保存到 /tmp/windows_recovery.txt"
cat /tmp/windows_recovery.txt

echo "=== 修复完成 ==="
echo "请重启系统并尝试启动Windows。"
echo "如果问题仍然存在，请按照上述恢复指南操作。"
