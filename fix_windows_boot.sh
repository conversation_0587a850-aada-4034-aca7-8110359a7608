#!/bin/bash

# Windows 10 启动修复脚本
# 用于修复CB4蓝屏错误

echo "=== Windows 10 启动修复工具 ==="
echo "正在检查系统状态..."

# 检查分区是否已挂载
if ! mountpoint -q /mnt/windows; then
    echo "挂载Windows分区..."
    sudo mkdir -p /mnt/windows
    sudo mount /dev/nvme0n1p2 /mnt/windows
fi

if ! mountpoint -q /mnt/efi; then
    echo "挂载EFI分区..."
    sudo mkdir -p /mnt/efi
    sudo mount /dev/nvme0n1p1 /mnt/efi
fi

echo "=== 方法1: 重建BCD启动配置 ==="
echo "备份当前BCD文件..."
sudo cp /mnt/efi/EFI/Microsoft/Boot/BCD /mnt/efi/EFI/Microsoft/Boot/BCD.backup

echo "=== 方法2: 检查Windows系统文件完整性 ==="
echo "检查关键系统文件是否存在..."

# 检查关键Windows文件
critical_files=(
    "/mnt/windows/Windows/System32/ntoskrnl.exe"
    "/mnt/windows/Windows/System32/hal.dll"
    "/mnt/windows/Windows/System32/drivers/disk.sys"
    "/mnt/windows/Windows/System32/drivers/classpnp.sys"
)

for file in "${critical_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file 存在"
    else
        echo "✗ $file 缺失 - 这可能是问题所在"
    fi
done

echo "=== 方法3: 重新创建Windows启动项 ==="
echo "删除现有Windows启动项..."
sudo efibootmgr -b 0000 -B 2>/dev/null || true

echo "重新创建Windows启动项..."
sudo efibootmgr -c -d /dev/nvme0n1 -p 1 -L "Windows Boot Manager" -l "\\EFI\\Microsoft\\Boot\\bootmgfw.efi"

echo "=== 方法4: 修复安全启动问题 ==="
echo "检查安全启动状态..."
if [ -d "/sys/firmware/efi" ]; then
    echo "系统使用UEFI启动"
    # 检查安全启动状态
    if [ -f "/sys/firmware/efi/efivars/SecureBoot-*" ]; then
        echo "检测到安全启动配置"
    fi
else
    echo "系统使用传统BIOS启动"
fi

echo "=== 完成 ==="
echo "修复操作已完成。请重启系统测试。"
echo ""
echo "如果问题仍然存在，可能需要："
echo "1. 在BIOS中禁用安全启动"
echo "2. 检查硬盘健康状态"
echo "3. 使用Windows安装盘进行系统修复"
echo "4. 考虑重新安装Windows"
