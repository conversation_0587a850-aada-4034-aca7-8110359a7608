#!/bin/bash

# Isaac Lab 快速启动脚本
# 作者：AI助手
# 日期：2025-08-08

echo "🚀 Isaac Lab 快速启动脚本"
echo "================================"

# 检查是否在Isaac Lab目录中
if [ ! -f "docker/container.py" ]; then
    echo "❌ 错误：请在Isaac Lab根目录中运行此脚本"
    echo "💡 提示：先运行 'cd IsaacLab'"
    exit 1
fi

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ 错误：Docker未运行或无权限访问"
    echo "💡 提示：运行 'sudo systemctl start docker' 或将用户添加到docker组"
    exit 1
fi

echo "✅ 环境检查通过"

# 函数：启动容器
start_container() {
    echo "🔧 启动Isaac Lab容器..."
    echo "⏳ 首次运行需要下载约15GB数据，请耐心等待..."
    python3 docker/container.py start
    
    if [ $? -eq 0 ]; then
        echo "✅ 容器启动成功！"
        return 0
    else
        echo "❌ 容器启动失败"
        return 1
    fi
}

# 函数：进入容器
enter_container() {
    echo "🚪 进入Isaac Lab容器..."
    python3 docker/container.py enter
}

# 函数：运行示例
run_examples() {
    echo "🎮 可用的示例："
    echo "1. 基础物理仿真"
    echo "2. CartPole强化学习环境"
    echo "3. 机械臂仿真"
    echo "4. 相机传感器"
    echo "5. 进入容器交互模式"
    echo "0. 退出"
    
    read -p "请选择要运行的示例 (0-5): " choice
    
    case $choice in
        1)
            echo "🌍 启动基础物理仿真..."
            python3 docker/container.py enter -c "isaaclab -p scripts/tutorials/00_sim/spawn_prims.py"
            ;;
        2)
            echo "🎯 启动CartPole强化学习环境..."
            python3 docker/container.py enter -c "isaaclab -p scripts/tutorials/03_envs/create_cartpole_rl_env.py --num_envs 32"
            ;;
        3)
            echo "🤖 启动机械臂仿真..."
            python3 docker/container.py enter -c "isaaclab -p scripts/tutorials/01_assets/run_articulation.py"
            ;;
        4)
            echo "📷 启动相机传感器..."
            python3 docker/container.py enter -c "isaaclab -p scripts/tutorials/04_sensors/run_camera.py"
            ;;
        5)
            echo "💻 进入交互模式..."
            enter_container
            ;;
        0)
            echo "👋 再见！"
            exit 0
            ;;
        *)
            echo "❌ 无效选择"
            run_examples
            ;;
    esac
}

# 主菜单
main_menu() {
    echo ""
    echo "📋 请选择操作："
    echo "1. 启动容器"
    echo "2. 进入容器"
    echo "3. 运行示例"
    echo "4. 停止容器"
    echo "5. 查看容器状态"
    echo "0. 退出"
    
    read -p "请输入选择 (0-5): " choice
    
    case $choice in
        1)
            start_container
            if [ $? -eq 0 ]; then
                main_menu
            fi
            ;;
        2)
            enter_container
            main_menu
            ;;
        3)
            run_examples
            main_menu
            ;;
        4)
            echo "🛑 停止容器..."
            python3 docker/container.py stop
            echo "✅ 容器已停止"
            main_menu
            ;;
        5)
            echo "📊 容器状态："
            docker ps -a | grep isaac
            main_menu
            ;;
        0)
            echo "👋 感谢使用Isaac Lab！"
            exit 0
            ;;
        *)
            echo "❌ 无效选择，请重新输入"
            main_menu
            ;;
    esac
}

# 显示欢迎信息
echo ""
echo "🤖 Isaac Lab Docker 环境"
echo "📖 版本：0.41.5 (Isaac Sim 4.5)"
echo "🖥️  支持图形界面和GPU加速"
echo ""

# 启动主菜单
main_menu
